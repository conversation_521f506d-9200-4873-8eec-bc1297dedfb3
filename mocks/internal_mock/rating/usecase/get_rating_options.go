// Code generated by MockGen. DO NOT EDIT.
// Source: get_rating_options.go
//
// Generated by this command:
//
//	mockgen -source get_rating_options.go -destination ../../../mocks/internal_mock/rating/usecase/get_rating_options.go -package usecase
//

// Package usecase is a generated GoMock package.
package usecase

import (
	context "context"
	reflect "reflect"

	repository "gitlab.viswalslab.com/backend/ratings/internal/rating/repository"
	gomock "go.uber.org/mock/gomock"
)

// MockgetRatingOptionsRepo is a mock of getRatingOptionsRepo interface.
type MockgetRatingOptionsRepo struct {
	ctrl     *gomock.Controller
	recorder *MockgetRatingOptionsRepoMockRecorder
	isgomock struct{}
}

// MockgetRatingOptionsRepoMockRecorder is the mock recorder for MockgetRatingOptionsRepo.
type MockgetRatingOptionsRepoMockRecorder struct {
	mock *MockgetRatingOptionsRepo
}

// NewMockgetRatingOptionsRepo creates a new mock instance.
func NewMockgetRatingOptionsRepo(ctrl *gomock.Controller) *MockgetRatingOptionsRepo {
	mock := &MockgetRatingOptionsRepo{ctrl: ctrl}
	mock.recorder = &MockgetRatingOptionsRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockgetRatingOptionsRepo) EXPECT() *MockgetRatingOptionsRepoMockRecorder {
	return m.recorder
}

// GetRatingOptions mocks base method.
func (m *MockgetRatingOptionsRepo) GetRatingOptions(ctx context.Context, filter *repository.GetRatingOptionsFilter) (*repository.GetRatingOptionsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRatingOptions", ctx, filter)
	ret0, _ := ret[0].(*repository.GetRatingOptionsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRatingOptions indicates an expected call of GetRatingOptions.
func (mr *MockgetRatingOptionsRepoMockRecorder) GetRatingOptions(ctx, filter any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRatingOptions", reflect.TypeOf((*MockgetRatingOptionsRepo)(nil).GetRatingOptions), ctx, filter)
}
