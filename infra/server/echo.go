package server

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"sort"
	"syscall"
	"time"

	"gitlab.viswalslab.com/backend/ratings/internal/country"
	"gitlab.viswalslab.com/backend/ratings/internal/rating"
	"gitlab.viswalslab.com/backend/ratings/pkg/cognito"

	"github.com/labstack/echo/v4"
	"gorm.io/gorm"

	"gitlab.viswalslab.com/backend/standard/v2/auth"

	amqp "github.com/rabbitmq/amqp091-go"

	"github.com/jmoiron/sqlx"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"

	"gitlab.viswalslab.com/backend/ratings/infra/config"
	"gitlab.viswalslab.com/backend/ratings/infra/server/route"
	"gitlab.viswalslab.com/backend/ratings/pkg/rabbitmq"
)

const timeout = time.Second * 10

// ApplicationRunner is the main entry point for the application.
type ApplicationRunner interface {
	Run()
}

// AppBuilder represents a builder for creating an application.
// It allows setting different dependencies such as router, database, gRPC,
// message broker, file manager, and configuration through its methods.
// Finally, the Build method returns an ApplicationRunner interface implementation.
type AppBuilder struct {
	ctx                    context.Context
	messagingContext       context.Context
	cancelMessagingContext context.CancelFunc
	cfg                    config.Configuration
	db                     *sqlx.DB
	dbGorm                 *gorm.DB
	router                 *echo.Echo
	baseGroup              *echo.Group
	logger                 vlog.Logger
	ac                     auth.Config
	messaging              *rabbitmq.RabbitMQ
	authProvider           *cognito.Cognito
}

// NewBuilder returns new instance of AppBuilder.
func NewBuilder() *AppBuilder {
	return &AppBuilder{}
}

// WithContext sets the context for the application.
func (app *AppBuilder) WithContext(ctx context.Context) *AppBuilder {
	app.ctx = ctx
	return app
}

// WithMessagingContext sets the context for the messaging queue.
func (app *AppBuilder) WithMessagingContext(ctx context.Context) *AppBuilder {
	app.messagingContext = ctx
	return app
}

func (app *AppBuilder) WithCancelMessagingContext(cancelFunc context.CancelFunc) *AppBuilder {
	app.cancelMessagingContext = cancelFunc
	return app
}

// WithConfiguration sets the configuration for the application.
func (app *AppBuilder) WithConfiguration(cfg config.Configuration) *AppBuilder {
	app.cfg = cfg
	return app
}

// WithDatabase sets the database for the application.
func (app *AppBuilder) WithDatabase(db *sqlx.DB) *AppBuilder {
	app.db = db
	return app
}

func (app *AppBuilder) WithGormDatabase(db *gorm.DB) *AppBuilder {
	app.dbGorm = db
	return app
}

// WithRouter sets the router for the application.
func (app *AppBuilder) WithRouter(echoRouter *echo.Echo) *AppBuilder {
	app.router = echoRouter
	return app
}

// WithBaseGroup sets the base group for the application.
func (app *AppBuilder) WithBaseGroup(group *echo.Group) *AppBuilder {
	app.baseGroup = group
	return app
}

// WithLogger sets the base group for the application.
func (app *AppBuilder) WithLogger(logger vlog.Logger) *AppBuilder {
	if logger == nil {
		logger = vlog.New()
	}

	app.logger = logger
	return app
}

func (app *AppBuilder) WithAuth(config auth.Config) *AppBuilder {
	app.ac = config
	return app
}

func (app *AppBuilder) WithAuthProvider(auth *cognito.Cognito) *AppBuilder {
	app.authProvider = auth
	return app
}

func (app *AppBuilder) WithMessaging(messaging *rabbitmq.RabbitMQ) *AppBuilder {
	app.messaging = messaging
	return app
}

// Build finalizes the building process and returns the ApplicationRunner instance.
func (app *AppBuilder) Build() (ApplicationRunner, error) {
	if app.ctx == nil {
		app.ctx = context.Background()
	}

	if app.logger == nil {
		vlog.New().Fatal("logger is missing")
	}

	if app.router == nil {
		app.logger.Fatal("router is missing")
	}

	if app.db == nil {
		app.logger.Fatal("database is missing")
	}

	if app.messaging == nil {
		app.logger.Fatal("messaging is missing")
	}

	return app, nil
}

func (app *AppBuilder) PrintRoutes() {
	routes := app.router.Routes()
	sort.Slice(routes, func(i, j int) bool {
		return routes[i].Path < routes[j].Path
	})

	fmt.Printf("\nServer Routes:")
	for _, r := range routes {
		fmt.Printf("\n%s\t %s", r.Method, r.Path)
	}
	fmt.Printf("\n")
}

// Run starts the server and handles incoming HTTP requests.
// It gracefully shuts down the server on interrupt signal.
func (app *AppBuilder) Run() {
	app.startSubscribers()
	app.bootstrapApplication()

	// Start the server in a separate goroutine with recovery
	go func() {
		defer func() {
			if r := recover(); r != nil {
				app.logger.Error("panic recovered in server", vlog.F("error", r))
			}
		}()

		app.logger.Info("starting server", vlog.F("port", app.cfg.Port))

		app.PrintRoutes()

		if err := app.router.Start(app.cfg.Port); err != nil && !errors.Is(err, http.ErrServerClosed) {
			app.logger.Fatal("shutting down the server due to error", vlog.F("error", err))
		}
	}()

	// handles listening for the queue connection close events.
	go func() {
		errChan := app.messaging.Conn.NotifyClose(make(chan *amqp.Error, 1))

		for {
			select {
			case e := <-errChan:
				// notify underlying go routine to close with context.
				app.cancelMessagingContext()

				if e == nil {
					app.logger.Info("queue connection closed")
					return
				}

				app.logger.Error("error from queue", vlog.F("error", e))

				// try to reestablish the connection with rabbitmq due to server error.
				mq, err := rabbitmq.NewRabbitMQ(app.ctx, app.cfg.Messaging)
				if err != nil {
					if errors.Is(err, context.Canceled) || errors.Is(err, context.DeadlineExceeded) {
						app.logger.Info("shutting down the queue", vlog.F("error", app.ctx.Err()))
						return
					}

					app.logger.Error("failed to create rabbitMQ instance", vlog.F("error", err))
				}

				// create a new listener for this new connection to listen for close events.
				errChan = mq.Conn.NotifyClose(make(chan *amqp.Error, 1))

				// update the context with new context.
				ctx, cancel := context.WithCancel(app.ctx)

				// embed context to app to handle graceful shutdown
				app.messagingContext = ctx
				app.cancelMessagingContext = cancel
				app.messaging = mq

				// start the infra for all rabbitmq consumers.
				go app.startSubscribers()

			// listen for the application context to be closed.
			case <-app.ctx.Done():
				app.logger.Info("shutting down the queue", vlog.F("error", app.ctx.Err()))
				app.cancelMessagingContext()
			}
		}
	}()

	// Graceful shutdown handling
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, os.Interrupt, syscall.SIGTERM)
	<-quit

	ctx, cancel := context.WithTimeout(app.ctx, timeout)
	defer cancel()

	// cancel the queue context to stop all consumers.
	app.cancelMessagingContext()

	if err := app.messaging.Close(); err != nil {
		app.logger.Error("error closing rabbitMQ connection", vlog.F("error", err))
	}

	if err := app.router.Shutdown(ctx); err != nil {
		app.logger.Error("error shutting down the server", vlog.F("error", err))
	}

	app.logger.Info("server shut down")
}

func (app *AppBuilder) bootstrapApplication() {
	var (
		userParseJWT = app.authProvider.ParseJWT(app.cfg.UserAuth.UserPoolID, app.cfg.UserAuth.DefaultRegion)

		// all factory instance here
		countryFactory = country.NewFactory(app.db)
		ratingFactory  = rating.NewFactory(app.db)
	)

	_ = userParseJWT // TODO: remove it

	// all route initialization here
	route.NewCountryRouter(app.baseGroup, countryFactory).Route()
	route.NewRatingRouter(app.baseGroup, ratingFactory).Route()
}

func (app *AppBuilder) startSubscribers() {
	// TODO: add all subscribers here
	var subscribers = []rabbitmq.Subscriber{
		// TODO:add subscriber here
	}

	if len(subscribers) == 0 {
		return
	}

	// create a retry channel for a listening event from internal go routines,
	// so we can schedule retry for rabbitmq components.
	var retryChannel = make(chan rabbitmq.ErrorListener, 10)

	// internal context to manage this go routine.
	ctx, cancel := context.WithCancel(app.messagingContext)

	// TODO: remove this if this is redundant.
	ctx = vlog.AttachLoggerToContext(ctx, app.logger)

	// starts all subscribers in a separate goroutine.
	// with a retry channel to listen for errors from the subscribers.
	for _, sub := range subscribers {
		go app.setupSubscriber(ctx, sub, retryChannel)
	}

	// create a go routine for handling the retry channel.
	go func() {
		for {
			select {

			case e := <-retryChannel:
				if e.Retryable {
					app.logger.Error("subscriber error", vlog.F("error", e.Err), vlog.F("queue_name", e.Subscriber.QueueName()))
					app.logger.Info("retrying subscriber", vlog.F("queue_name", e.Subscriber.QueueName()))

					// start retrying the subscriber from creating a channel.
					go app.setupSubscriber(ctx, e.Subscriber, retryChannel)

				} else {
					app.logger.Error("error from subscriber", vlog.F("error", e.Err), vlog.F("queue_name", e.Subscriber.QueueName()))
				}

			case <-app.messagingContext.Done():
				app.logger.Info("shutting down the subscriber", vlog.F("error", app.messagingContext.Err()))
				cancel()
				return
			}
		}
	}()
}

// setupSubscriber sets up a subscriber with its own channel and queue.
func (app *AppBuilder) setupSubscriber(ctx context.Context, s rabbitmq.Subscriber, retryChan chan rabbitmq.ErrorListener) {
	internalCtx, cancel := context.WithCancel(ctx)
	defer cancel()

	app.logger.With(vlog.F("subscriber", s.QueueName()))

	defer func() {
		if r := recover(); r != nil {
			app.logger.Error("subscriber goroutine panicked",
				vlog.F("queue_name", s.QueueName()),
				vlog.F("recover", r),
			)
		}
	}()

	app.logger.Info("attempting to subscribe to queue", vlog.F("queue_name", s.QueueName()))

	// creates a channel with inbuilt retry mechanism.
	ch, err := app.messaging.Channel(internalCtx)
	if err != nil {
		if errors.Is(err, context.Canceled) || errors.Is(err, context.DeadlineExceeded) {
			app.logger.Info("shutting down the channel", vlog.F("error", err))
			return
		}
		if errors.Is(err, rabbitmq.ErrNotInitialized) {
			app.logger.Error("rabbitmq is not initialised yet please check config", vlog.F("error", err))
			return
		}

		app.logger.Error("error creating rabbitMQ channel", vlog.F("error", err))
		return
	}

	defer ch.Close()
	defer cancel()
	app.logger.Info("created channel for queue", vlog.F("queue_name", s.QueueName()))

	// errChanRMQ listen for error from the rabbitmq connection channel.
	errChanRMQ := ch.NotifyClose(make(chan *amqp.Error, 1))

	// subscribe to the queue with inbuilt retry mechanism.
	msgs, err := app.messaging.SubscribeToQueue(internalCtx, ch, s.QueueName())
	if err != nil {
		if errors.Is(err, context.Canceled) || errors.Is(err, context.DeadlineExceeded) {
			app.logger.Info("shutting down the queue", vlog.F("error", err))
			return
		}
		if errors.Is(err, rabbitmq.ErrNotInitialized) {
			app.logger.Error("rabbitmq is not initialised yet please check config", vlog.F("error", err))
			return
		}

		// in case of queue doesn't exist retry connection logic using retryChan
		retryChan <- rabbitmq.ErrorListener{
			Err:        errors.Join(fmt.Errorf("failed to register queue"), err),
			Subscriber: s,
			// due to rMQ behavior of closing the channel when queue operation fails we should retry this error.
			Retryable: true, // if queue subscription failed, it should be retried again through the retryChan.
		}
		return
	}

	app.logger.Info("subscribed consumer for queue", vlog.F("queue_name", s.QueueName()))

	// listen for any closing event from rabbitmq.
	go func() {
		e := <-errChanRMQ
		if e != nil {
			// invoke retry function for re-establish the connection
			retryChan <- rabbitmq.ErrorListener{
				Err:        errors.Join(fmt.Errorf("error from queue"), e),
				Subscriber: s,
				Retryable:  true,
			}
			return
		} else {
			// on the gradual shutdown, rmq will return nil error
			app.logger.Info("closed rabbitmq channel successful")
			return
		}
	}()

	// listen for any incoming messages from the queue.
	err = app.messaging.HandleMessage(internalCtx, msgs, s)
	if err != nil {
		if errors.Is(err, context.DeadlineExceeded) || errors.Is(err, context.Canceled) {
			app.logger.Info("message handler closed", vlog.F("error", err))
			return
		} else {
			retryChan <- rabbitmq.ErrorListener{
				Err:        errors.Join(fmt.Errorf("error from queue"), err),
				Subscriber: s,
				Retryable:  true,
			}
			return
		}
	}
}
