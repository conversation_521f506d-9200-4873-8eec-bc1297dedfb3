package usecase_test

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"gitlab.viswalslab.com/backend/ratings/internal/rating/entity"
	"gitlab.viswalslab.com/backend/ratings/internal/rating/repository"
	uc "gitlab.viswalslab.com/backend/ratings/internal/rating/usecase"
	mockUC "gitlab.viswalslab.com/backend/ratings/mocks/internal_mock/rating/usecase"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
	"go.uber.org/mock/gomock"
)

func TestGetRatingOptionsUsecase_Execute(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	logger := vlog.New()
	ctx := vlog.AttachLoggerToContext(context.Background(), logger)

	mockRepo := mockUC.NewMockgetRatingOptionsRepo(ctrl)
	u := uc.NewGetRatingOptionsUsecase(mockRepo)

	t.Run("success", func(t *testing.T) {
		// Create mock entities
		entity1, _ := entity.NewRatingOption(&entity.NewRatingOptionInput{
			ID:                1,
			OptionType:        "overall_experience",
			Value:             "Excellent service",
			ApplicableToRoles: []string{"user", "pro"},
			MinStars:          intPtr(4),
			FieldTag:          stringPtr("service"),
			DisplayOrder:      1,
		})

		entity2, _ := entity.NewRatingOption(&entity.NewRatingOptionInput{
			ID:                2,
			OptionType:        "professional_skills",
			Value:             "Great technical skills",
			ApplicableToRoles: []string{"pro"},
			MinStars:          nil,
			FieldTag:          nil,
			DisplayOrder:      2,
		})

		mockResponse := &repository.GetRatingOptionsResponse{
			Options: []*entity.RatingOption{entity1, entity2},
		}

		mockRepo.EXPECT().GetRatingOptions(gomock.Any(), gomock.Any()).Return(mockResponse, nil)

		input := &uc.GetRatingOptionsInput{
			OptionType:        stringPtr("overall_experience"),
			ApplicableToRoles: []string{"user"},
			MinStars:          4,
			FieldTag:          stringPtr("service"),
		}

		output, err := u.Execute(ctx, input)
		assert.NoError(t, err)
		assert.NotNil(t, output)
		assert.Len(t, output.Options, 2)
		assert.Equal(t, "Excellent service", output.Options[0].Value)
		assert.Equal(t, "Great technical skills", output.Options[1].Value)
	})

	t.Run("success_with_nil_input", func(t *testing.T) {
		mockResponse := &repository.GetRatingOptionsResponse{
			Options: []*entity.RatingOption{},
		}

		mockRepo.EXPECT().GetRatingOptions(gomock.Any(), nil).Return(mockResponse, nil)

		output, err := u.Execute(ctx, nil)
		assert.NoError(t, err)
		assert.NotNil(t, output)
		assert.Len(t, output.Options, 0)
	})

	t.Run("repository_error", func(t *testing.T) {
		mockRepo.EXPECT().GetRatingOptions(gomock.Any(), gomock.Any()).Return(nil, errors.New("database error"))

		input := &uc.GetRatingOptionsInput{
			OptionType: stringPtr("overall_experience"),
		}

		output, err := u.Execute(ctx, input)
		assert.Error(t, err)
		assert.Nil(t, output)
		assert.Contains(t, err.Error(), "database error")
	})
}

func TestNewGetRatingOptionsUsecase(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mockUC.NewMockgetRatingOptionsRepo(ctrl)

	t.Run("success", func(t *testing.T) {
		usecase := uc.NewGetRatingOptionsUsecase(mockRepo)
		assert.NotNil(t, usecase)
	})

	t.Run("with_nil_repo", func(t *testing.T) {
		usecase := uc.NewGetRatingOptionsUsecase(nil)
		assert.NotNil(t, usecase)
	})
}

// Helper functions
func stringPtr(s string) *string {
	return &s
}

func intPtr(i int) *int {
	return &i
}
