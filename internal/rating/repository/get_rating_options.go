package repository

import (
	"context"
	"fmt"

	"github.com/lib/pq"
	"gitlab.viswalslab.com/backend/ratings/internal/rating/entity"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

// RatingOptionModel represents the database structure for rating options
type RatingOptionModel struct {
	ID                int            `db:"id"`
	OptionType        string         `db:"option_type"`
	Value             string         `db:"option_value"`
	ApplicableToRoles pq.StringArray `db:"applicable_to_roles"`
	MinStars          *int           `db:"min_stars"`
	FieldTag          *string        `db:"field_tag"`
	DisplayOrder      int            `db:"display_order"`
}

type GetRatingOptionsFilter struct {
	OptionType        *string
	ApplicableToRoles []string
	MinStars          int
	FieldTag          *string
}

type GetRatingOptionsResult struct {
	Options []RatingOptionModel
}

func (r *ratingRepo) GetRatingOptions(ctx context.Context, filter *GetRatingOptionsFilter) (*GetRatingOptionsResult, error) {
	logger := vlog.FromContext(ctx).With(vlog.F("method", "getRatingOptions"), vlog.F("action", "get rating options with filter"))

	query := `
		SELECT id, option_type, option_value, applicable_to_roles, min_stars, field_tag, display_order
		FROM rating_options
		WHERE 1 = 1`

	var args []any
	argIndex := 1

	if filter != nil {
		if filter.OptionType != nil {
			query += ` AND option_type = $` + fmt.Sprintf("%d", argIndex)
			args = append(args, *filter.OptionType)
			argIndex++
		}

		if len(filter.ApplicableToRoles) > 0 {
			query += ` AND applicable_to_roles && $` + fmt.Sprintf("%d", argIndex)
			args = append(args, pq.Array(filter.ApplicableToRoles))
			argIndex++
		}

		// MinStars is mandatory, so always add this filter
		query += ` AND min_stars = $` + fmt.Sprintf("%d", argIndex)
		args = append(args, filter.MinStars)
		argIndex++

		if filter.FieldTag != nil {
			query += ` AND (field_tag IS NULL OR field_tag = $` + fmt.Sprintf("%d", argIndex) + `)`
			args = append(args, *filter.FieldTag)
			argIndex++
		}
	}

	query += ` ORDER BY display_order ASC`

	logger.Debug("executing get rating options query", vlog.F("query", query), vlog.F("args", args))

	rows, err := r.db.QueryxContext(ctx, query, args...)
	if err != nil {
		logger.Error("failed to get rating options", vlog.F("error", err))
		return nil, err
	}
	defer rows.Close()

	var options []RatingOptionModel
	for rows.Next() {
		var option RatingOptionModel
		err := rows.StructScan(&option)
		if err != nil {
			logger.Error("failed to scan rating option", vlog.F("error", err))
			return nil, err
		}
		options = append(options, option)
	}

	if err = rows.Err(); err != nil {
		logger.Error("error iterating rating options", vlog.F("error", err))
		return nil, err
	}

	logger.Debug("rating options retrieved successfully", vlog.F("count", len(options)))
	return &GetRatingOptionsResult{Options: options}, nil
}

// convertDTOsToEntities converts RatingOptionModel DTOs to entity.RatingOption entities
func convertDTOsToEntities(dtos []RatingOptionModel) ([]*entity.RatingOption, error) {
	entities := make([]*entity.RatingOption, 0, len(dtos))

	for _, dto := range dtos {
		input := &entity.NewRatingOptionInput{
			ID:                dto.ID,
			OptionType:        dto.OptionType,
			Value:             dto.Value,
			ApplicableToRoles: []string(dto.ApplicableToRoles),
			MinStars:          dto.MinStars,
			FieldTag:          dto.FieldTag,
			DisplayOrder:      dto.DisplayOrder,
		}

		entity, err := entity.NewRatingOption(input)
		if err != nil {
			return nil, err
		}

		entities = append(entities, entity)
	}

	return entities, nil
}
