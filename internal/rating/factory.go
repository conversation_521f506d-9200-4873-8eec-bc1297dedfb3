package rating

import (
	"github.com/jmoiron/sqlx"
	"gitlab.viswalslab.com/backend/ratings/internal/rating/repository"
	"gitlab.viswalslab.com/backend/ratings/internal/rating/transport"
	"gitlab.viswalslab.com/backend/ratings/internal/rating/usecase"
)

type factory struct {
	db *sqlx.DB
}

func NewFactory(db *sqlx.DB) *factory {
	return &factory{
		db: db,
	}
}

func (f *factory) GetRatingOptionsHandler() *transport.GetRatingOptionsHandler {
	repo := repository.NewRatingRepo(f.db)
	uc := usecase.NewGetRatingOptionsUsecase(repo)
	h := transport.NewGetRatingOptionsHandler(uc)

	return h
}
